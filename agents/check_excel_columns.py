import pandas as pd
from pathlib import Path

# File paths
file1 = Path("/Users/<USER>/Downloads/My_Timesheet (7).xlsx")
file2 = Path("/Users/<USER>/Downloads/My_Timesheet (8).xlsx")

def check_file(file_path):
    """Check if file exists and print its columns."""
    print(f"\nChecking file: {file_path}")
    
    if not file_path.exists():
        print(f"❌ File does not exist: {file_path}")
        return
    
    print(f"✅ File exists: {file_path}")
    
    try:
        # Read the Excel file
        xl = pd.ExcelFile(file_path)
        
        # Print sheet names
        print(f"Sheet names: {xl.sheet_names}")
        
        # Check each sheet
        for sheet in xl.sheet_names:
            print(f"\nSheet: {sheet}")
            df = xl.parse(sheet)
            print(f"Columns: {df.columns.tolist()}")
            print(f"Number of rows: {len(df)}")
            print(f"First few rows:")
            print(df.head(2))
    
    except Exception as e:
        print(f"❌ Error reading file: {e}")

# Check both files
check_file(file1)
check_file(file2)
