import random
import logging
from transformers import AutoModelForCausalLM, AutoTokenizer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuoteGenerator:
    """
    Generates marriage quotes using the EleutherAI/gpt-neo-125M model.
    Falls back to a predefined list if there are any issues with the model.
    """

    def __init__(self):
        """Initialize the quote generator with the GPT-Neo model."""
        # Fallback quotes in case the model fails
        self.fallback_quotes = [
            "A successful marriage requires falling in love many times, always with the same person.",
            "Marriage is not just spiritual communion, it is also remembering to take out the trash.",
            "The secret of a happy marriage is finding the right person. You know they're right if you love being with them all the time.",
            "A great marriage is not when the 'perfect couple' comes together. It is when an imperfect couple learns to enjoy their differences.",
            "Marriage is like a garden. It takes time, effort, and patience to cultivate something beautiful.",
            "The first to apologize is the bravest. The first to forgive is the strongest. The first to forget is the happiest.",
            "Marriage is not about finding a person you can live with, it's about finding the person you can't live without.",
            "Love is patient, love is kind. It does not envy, it does not boast, it is not proud.",
            "A happy marriage is about three things: memories of togetherness, forgiveness of mistakes, and a promise to never give up on each other.",
            "The best thing to hold onto in life is each other.",
            "Marriage is a commitment to do what it takes to make the relationship work. It's a promise to love in good times and bad.",
            "In marriage, each partner is to be an encourager rather than a critic, a forgiver rather than a collector of hurts.",
            "Marriage is a thousand little things. It's giving up your right to be right in the heat of an argument.",
            "The greatest marriages are built on teamwork, mutual respect, a healthy dose of admiration, and a never-ending portion of love and grace.",
            "Marriage is not a noun; it's a verb. It isn't something you get. It's something you do. It's the way you love your partner every day.",
            "A long-lasting marriage is built on trust, respect, and forgiveness. It's about choosing to love each other even in those moments when you struggle to like each other.",
            "The beauty of marriage is not always seen from the beginning—but rather as love grows and develops over time.",
            "Marriage is a daily commitment to grow together, to work through every joy and pain together, and to love each other fully.",
            "A good marriage is one where each partner secretly suspects they got the better deal.",
            "Marriage is the highest form of teamwork, where two people become one without losing their individuality.",
            "The most important thing a father can do for his children is to love their mother.",
            "Marriage is a sacred bond that unites two souls in a covenant of love, trust, and mutual respect.",
            "In the arithmetic of love, one plus one equals everything, and two minus one equals nothing.",
            "A godly marriage is a reflection of God's love for His people—unconditional, sacrificial, and enduring.",
            "The strength of marriage lies in learning to share your life, and always putting the other person first."
        ]

        # Marriage-related prompts for the model
        self.prompts = [
            "A beautiful quote about marriage is: ",
            "The secret to a successful marriage is: ",
            "Marriage advice: ",
            "The most important thing in a marriage is: ",
            "A loving marriage requires: ",
            "The foundation of a strong marriage is: ",
            "For a marriage to thrive, couples must: ",
            "The beauty of marriage is that: ",
            "Marriage teaches us that: ",
            "In marriage, love means: ",
            "The key to a lasting marriage is: ",
            "Marriage is a sacred commitment because: ",
            "A godly marriage is built on: ",
            "The purpose of marriage is: ",
            "Marriage brings joy when: "
        ]

        # Try to initialize the model
        try:
            logger.info("Loading GPT-Neo model...")
            self.model_name = "EleutherAI/gpt-neo-125M"
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForCausalLM.from_pretrained(self.model_name)
            logger.info("GPT-Neo model loaded successfully")
            self.use_model = True
        except Exception as e:
            logger.warning(f"Failed to load GPT-Neo model: {str(e)}")
            logger.info("Falling back to predefined quotes")
            self.use_model = False

    def generate_quote(self):
        """
        Generate a marriage quote using the GPT-Neo model.

        Returns:
            str: A marriage quote
        """
        if self.use_model:
            try:
                # Select a random prompt about marriage
                prompt = random.choice(self.prompts)

                # Encode the prompt
                input_ids = self.tokenizer.encode(prompt, return_tensors="pt")

                # Generate the text
                output = self.model.generate(
                    input_ids=input_ids,
                    max_length=50,
                    temperature=0.7,
                    top_k=50,
                    top_p=0.95,
                    do_sample=True,
                    num_return_sequences=1,
                    no_repeat_ngram_size=2
                )

                # Decode the output
                generated_text = self.tokenizer.decode(output[0], skip_special_tokens=True)

                # Format the text as a quote
                quote = self._format_as_quote(generated_text, prompt)

                logger.info(f"Generated quote from prompt: '{prompt}'")

                # If the quote is too short, use a fallback
                if len(quote) < 20:
                    return random.choice(self.fallback_quotes)

                return quote

            except Exception as e:
                logger.warning(f"Error generating quote with model: {str(e)}")
                return random.choice(self.fallback_quotes)
        else:
            return random.choice(self.fallback_quotes)

    def _format_as_quote(self, text, prompt):
        """
        Format the model's output as a marriage quote.

        Args:
            text (str): The raw text from the model
            prompt (str): The prompt used to generate the text

        Returns:
            str: A formatted quote
        """
        # Clean up the text
        text = text.strip()

        # Extract the generated part (remove the prompt)
        if text.startswith(prompt):
            quote = text[len(prompt):].strip()
        else:
            quote = text

        # Find the first complete sentence
        sentences = []
        current = ""
        for char in quote:
            current += char
            if char in ['.', '!', '?'] and len(current.strip()) > 10:
                sentences.append(current.strip())
                current = ""

        # If we found complete sentences, use the first one
        if sentences:
            quote = sentences[0]
        else:
            # Otherwise use the whole text but limit it
            quote = quote[:100].strip()

        # Capitalize the first letter
        if quote:
            quote = quote[0].upper() + quote[1:]

        # Add a period at the end if missing
        if quote and not quote.endswith(('.', '!', '?')):
            quote += '.'

        return quote
