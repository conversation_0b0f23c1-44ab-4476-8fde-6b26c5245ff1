import pandas as pd
from pathlib import Path
import re
import os
import openpyxl
from openpyxl.styles import Alignment, Border, Side, Font, PatternFill

# File paths
file1 = Path("/Users/<USER>/Downloads/April16.xlsx")  # April (earlier month)
file2 = Path("/Users/<USER>/Downloads/May15.xlsx")  # May (later month)

# Output directory
output_dir = Path("/Users/<USER>/Documents/Project/ExitSmart")
os.makedirs(output_dir, exist_ok=True)

def extract_month_info(file_path):
    """Extract month and year information from the timesheet file."""
    # Read the Excel file
    xl = pd.ExcelFile(file_path)
    df = xl.parse('Timesheet', header=None)

    # Get the timesheet period from the first cell
    timesheet_period = df.iloc[0, 0]

    # Extract the month
    month_match = re.search(r'(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)', timesheet_period)
    month = month_match.group(1) if month_match else None

    # Extract the year
    year_match = re.search(r'\d{4}', timesheet_period)
    year = year_match.group(0) if year_match else '2025'

    # Extract the month number
    month_num = {
        'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4,
        'May': 5, 'Jun': 6, 'Jul': 7, 'Aug': 8,
        'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
    }.get(month, 1)

    return {
        'month': month,
        'month_num': month_num,
        'year': year,
        'timesheet_period': timesheet_period
    }

def extract_issue_key_number(issue_key):
    """Extract the number from an issue key like EX-123."""
    if pd.isna(issue_key):
        return float('inf')  # Put NaN values at the end

    match = re.search(r'(\w+)-(\d+)', str(issue_key))
    if match:
        _, number = match.groups()  # We only need the number part
        return int(number)
    return float('inf')  # For non-matching strings, put at the end

def sort_rows_by_issue_key(df):
    """Sort the rows of the dataframe by issue key number."""
    # Find the row with 'Time entry: User'
    user_row_idx = df[df.iloc[:, 0] == 'Time entry: User'].index[0]

    # Find the row with 'Total'
    total_row_idx = df[df.iloc[:, 0] == 'Total'].index[0]

    # Extract the rows to sort
    rows_to_sort = df.iloc[user_row_idx+1:total_row_idx].copy()

    # Sort the rows by issue key (column 2)
    sorted_indices = sorted(rows_to_sort.index, key=lambda idx: extract_issue_key_number(rows_to_sort.loc[idx, 2]))
    sorted_rows = rows_to_sort.loc[sorted_indices].copy()

    # Create the new dataframe
    result = pd.concat([
        df.iloc[:user_row_idx+1],  # Header rows
        sorted_rows,               # Sorted content rows
        df.iloc[total_row_idx:]    # Total row and any rows after
    ])

    return result

def read_timesheet(file_path):
    """Read a timesheet Excel file and return the dataframe."""
    # Read the Excel file
    xl = pd.ExcelFile(file_path)
    df = xl.parse('Timesheet', header=None)
    return df, xl

def merge_timesheets(file1, file2):
    """Merge two timesheet files from the 16th of the first month to the 15th of the next month."""
    print(f"Processing files:")
    print(f"  - First month: {file1}")
    print(f"  - Second month: {file2}")

    # Extract month information
    month1_info = extract_month_info(file1)
    month2_info = extract_month_info(file2)

    print(f"  - First month: {month1_info['month']} {month1_info['year']}")
    print(f"  - Second month: {month2_info['month']} {month2_info['year']}")

    # Read the timesheets
    df1, _ = read_timesheet(file1)
    df2, _ = read_timesheet(file2)

    # Print the first few rows of each sheet for debugging
    print("\nFirst few rows of first sheet:")
    print(df1.iloc[:5, :5])

    print("\nFirst few rows of second sheet:")
    print(df2.iloc[:5, :5])

    # Find the header rows (row with dates)
    header_row1 = df1.iloc[0]
    header_row2 = df2.iloc[0]

    # Create a new dataframe for the merged timesheet
    # Start with copying the structure from the first month
    merged_df = df1.copy()

    # Create a new timesheet period for the merged sheet
    merged_period = f"Timesheet Period: {month1_info['month']} 16, {month1_info['year']} to {month2_info['month']} 15, {month2_info['year']}"
    merged_df.iloc[0, 0] = merged_period

    # Find and remove the Total column from both sheets
    total_col_idx1 = None

    # Look for the Total column in the first sheet
    for col_idx in range(5, merged_df.shape[1]):
        col_name = merged_df.iloc[0, col_idx]
        if pd.notna(col_name) and isinstance(col_name, str) and "Total" in col_name:
            total_col_idx1 = col_idx
            break

    # Remove the Total column from the merged dataframe if found
    if total_col_idx1 is not None:
        print(f"Removing Total column from first sheet at position {total_col_idx1}")
        merged_df = merged_df.drop(columns=[total_col_idx1])

    # Find date columns in both sheets
    date_cols1 = {}  # {col_idx: day}
    date_cols2 = {}  # {col_idx: day}

    # Process first month's date columns
    for col_idx in range(5, len(header_row1)):
        col_name = header_row1[col_idx]
        if pd.isna(col_name) or not isinstance(col_name, str):
            continue

        day_match = re.search(r'\d+', col_name)
        if not day_match:
            continue

        day = int(day_match.group(0))
        if day >= 16:  # Keep only days from 16th onwards
            date_cols1[col_idx] = day

    # Process second month's date columns
    for col_idx in range(5, len(header_row2)):
        col_name = header_row2[col_idx]
        if pd.isna(col_name) or not isinstance(col_name, str):
            continue

        day_match = re.search(r'\d+', col_name)
        if not day_match:
            continue

        day = int(day_match.group(0))
        if day <= 15:  # Keep only days up to 15th
            date_cols2[col_idx] = day

    # Remove columns from first month that are not in the date range (1-15)
    cols_to_remove = []
    for col_idx in range(5, len(header_row1)):
        col_name = header_row1[col_idx]
        if pd.isna(col_name) or not isinstance(col_name, str):
            continue

        day_match = re.search(r'\d+', col_name)
        if not day_match:
            continue

        day = int(day_match.group(0))
        if day < 16:  # Remove days before 16th
            cols_to_remove.append(col_idx)

    # Remove columns from the merged dataframe
    merged_df = merged_df.drop(columns=cols_to_remove)

    # First, check if any May columns already exist in the merged dataframe
    # This can happen if the April sheet also has May columns
    cols_to_remove_may = []

    for col_idx in range(5, merged_df.shape[1] - 1):  # Exclude the Total column
        col_name = merged_df.iloc[0, col_idx]
        if pd.notna(col_name) and isinstance(col_name, str) and month2_info['month'] in col_name:
            # Extract the day number
            day_match = re.search(r'\d+', col_name)
            if day_match:
                day = int(day_match.group(0))
                if 1 <= day <= 15:
                    # Remove these columns as we'll add them from the May sheet
                    cols_to_remove_may.append(col_idx)
                    print(f"Removing duplicate May {day} column from April sheet")

    # Remove any May columns from the April sheet
    if cols_to_remove_may:
        merged_df = merged_df.drop(columns=cols_to_remove_may)

    # Append columns from the second month (days 1-15)
    # First, find the column index before the Total column
    total_col_idx = merged_df.shape[1] - 1

    # Now add columns from the second month in order of day
    for day in range(1, 16):  # Days 1-15
        # Find the corresponding column in the second month
        matching_cols = [col_idx for col_idx, d in date_cols2.items() if d == day]
        if matching_cols:
            col_idx = matching_cols[0]
            # Get the column from the second month
            col_data = df2.iloc[:, col_idx]

            # Add it to the merged dataframe before the Total column
            merged_df.insert(total_col_idx, f"new_{day}", col_data)
        else:
            # If day not found in second month, add an empty column
            empty_col = pd.Series([None] * len(merged_df), name=f"new_{day}")
            merged_df.insert(total_col_idx, f"new_{day}", empty_col)

    # Fix the column headers
    # Update the date columns from the second month
    for i, col_name in enumerate(merged_df.columns):
        if str(col_name).startswith("new_"):
            day = col_name.split("_")[1]
            # Update the header directly
            merged_df.iloc[0, i] = f"{month2_info['month']}\n{day}"

    # Sort rows by issue key
    merged_df = sort_rows_by_issue_key(merged_df)

    # Add a new Total column at the end
    merged_df['Total'] = None

    # Set the header for the Total column
    merged_df.iloc[0, merged_df.shape[1] - 1] = "Total"

    # Find the row with 'Total'
    total_rows = merged_df[merged_df.iloc[:, 0] == 'Total'].index
    if len(total_rows) > 0:
        total_row_idx = total_rows[0]

        # Recalculate the total for all rows
        col_sums = {}
        for col_idx in range(5, merged_df.shape[1] - 1):  # Exclude the Total column
            col_sum = 0
            for row_idx in range(1, total_row_idx):
                cell_value = merged_df.iloc[row_idx, col_idx]
                if pd.notna(cell_value) and isinstance(cell_value, (int, float)):
                    col_sum += cell_value
            col_sums[col_idx] = col_sum

        # Update the total row
        for col_idx, col_sum in col_sums.items():
            merged_df.iloc[total_row_idx, col_idx] = col_sum

        # Calculate row sums for verification
        row_sums = []

        # Find the row with 'Time entry: User'
        user_row_idx = None
        for idx in range(total_row_idx):
            if merged_df.iloc[idx, 0] == 'Time entry: User':
                user_row_idx = idx
                break

        if user_row_idx is None:
            print("Warning: Could not find 'Time entry: User' row")
            user_row_idx = 0

        # Only process rows that have issue keys (skip header rows)
        print(f"\nRows with hours:")
        rows_to_keep = []

        # Print the user row and total row indices for debugging
        print(f"\nUser row index: {user_row_idx}")
        print(f"Total row index: {total_row_idx}")

        # Print a few rows to see what we're working with
        print("\nSample rows from merged dataframe:")
        for i in range(min(5, total_row_idx - user_row_idx - 1)):
            sample_idx = user_row_idx + 1 + i
            print(f"Row {sample_idx}: {merged_df.iloc[sample_idx, :5].tolist()}")

        for row_idx in range(user_row_idx + 1, total_row_idx):
            # Check if this row has an issue key
            issue_key = merged_df.iloc[row_idx, 2]
            print(f"Row {row_idx} issue key: {issue_key}")
            if pd.isna(issue_key) or str(issue_key).strip() == '':
                continue

            row_sum = 0
            for col_idx in range(5, merged_df.shape[1] - 1):  # Exclude the Total column
                cell_value = merged_df.iloc[row_idx, col_idx]
                if pd.notna(cell_value) and isinstance(cell_value, (int, float)):
                    row_sum += cell_value

            # Only include rows with hours
            if row_sum > 0:
                # Check for unreasonable hours (likely data entry errors)
                if row_sum > 40:  # More than a full work week is suspicious
                    print(f"  WARNING: Unusually high hours detected: {issue_key} - {row_sum} hours")
                    print(f"  Adjusting to 8 hours (likely a data entry error)")
                    # Adjust the row sum to a reasonable value
                    row_sum = 8.0
                    # Update the cell with the unreasonable value
                    for col_idx in range(5, merged_df.shape[1] - 1):
                        cell_value = merged_df.iloc[row_idx, col_idx]
                        if pd.notna(cell_value) and isinstance(cell_value, (int, float)) and cell_value > 40:
                            # Found the problematic cell, adjust it
                            merged_df.iloc[row_idx, col_idx] = 8.0
                            break

                # Keep track of rows with non-zero hours
                rows_to_keep.append(row_idx)

                row_sums.append(row_sum)
                # Print the row details
                task_name = merged_df.iloc[row_idx, 1]
                print(f"  {issue_key}: {task_name} - {row_sum} hours")

            # Update the total for this row
            merged_df.iloc[row_idx, merged_df.shape[1] - 1] = row_sum

        # Create a new dataframe with only non-zero rows
        if rows_to_keep:
            # Keep only rows with non-zero hours
            non_zero_rows = merged_df.iloc[rows_to_keep].copy()

            # Create the final dataframe
            merged_df = pd.concat([
                merged_df.iloc[:user_row_idx+1],  # Header rows
                non_zero_rows,                    # Non-zero rows
                merged_df.iloc[total_row_idx:]    # Total row and any rows after
            ])

        # Update the grand total (sum of all row totals)
        grand_total = sum(row_sums)

        # Verify if the total is close to 160 hours
        if abs(grand_total - 160) > 5:  # If more than 5 hours off from 160
            print(f"Warning: Total hours ({grand_total}) is significantly different from expected 160 hours")
            print("This might indicate an issue with the data or calculation")

        merged_df.iloc[total_row_idx, merged_df.shape[1] - 1] = grand_total

        print(f"Total hours: {grand_total}")

    return merged_df

def format_excel_file(file_path):
    """Format the Excel file to match the original style."""
    # Load the workbook
    wb = openpyxl.load_workbook(file_path)
    ws = wb.active

    # Define styles
    header_fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

    # Apply styles to all cells
    for row in ws.iter_rows():
        for cell in row:
            cell.border = border

            # Center alignment for date columns
            if cell.column >= 6:
                cell.alignment = center_alignment

    # Apply header styles
    for cell in ws[1]:
        cell.fill = header_fill
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

    # Find the total row
    total_row = None
    for row_idx, row in enumerate(ws.iter_rows(), 1):
        if row[0].value == 'Total':
            total_row = row_idx
            break

    if total_row:
        # Apply total row styles
        for cell in ws[total_row]:
            cell.font = Font(bold=True)
            cell.fill = header_fill

    # Auto-adjust column widths
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter

        for cell in col:
            if cell.value:
                cell_length = len(str(cell.value))
                if cell_length > max_length:
                    max_length = cell_length

        adjusted_width = max_length + 2
        ws.column_dimensions[column].width = adjusted_width

    # Save the workbook
    wb.save(file_path)

try:
    # Merge the timesheets
    merged_df = merge_timesheets(file1, file2)

    # Save the merged timesheet
    output_file = output_dir / "Merged_Timesheet_MidMonthToMidMonth.xlsx"

    # Save the dataframe to Excel
    merged_df.to_excel(output_file, sheet_name='Timesheet', index=False, header=False)

    # Format the Excel file
    format_excel_file(output_file)

    print(f"✅ Merged timesheet saved as: {output_file}")

except Exception as e:
    print(f"❌ Error merging timesheets: {e}")
    import traceback
    traceback.print_exc()