import pandas as pd
from pathlib import Path
import openpyxl
from openpyxl.utils.cell import get_column_letter
from openpyxl.styles import Alignment, Border, Side, Font, PatternFill

# File paths
input_file = Path("/Users/<USER>/Downloads/April16ToMay15.xlsx")
output_dir = Path("/Users/<USER>/Documents/Project/ExitSmart")
output_file = output_dir / "April16ToMay15_Cleaned.xlsx"

# Ensure output directory exists
output_dir.mkdir(parents=True, exist_ok=True)

def remove_rows_with_zeros(input_path, output_path):
    """Load an Excel file, remove rows with zeros in any column, and save the cleaned file."""
    print(f"Processing file: {input_path}")

    # Load the workbook to preserve formulas and formatting
    wb = openpyxl.load_workbook(input_path, data_only=False)
    ws = wb.active

    # Identify rows to remove (rows with zero in any column)
    rows_to_remove = []
    for row_idx, row in enumerate(ws.iter_rows(values_only=True), 1):
        if any(cell == 0 for cell in row if isinstance(cell, (int, float))):
            rows_to_remove.append(row_idx)
            print(f"Marking row {row_idx} for removal (contains zero)")

    # Remove rows in reverse order to avoid shifting issues
    for row_idx in sorted(rows_to_remove, reverse=True):
        ws.delete_rows(row_idx)
        print(f"Removed row {row_idx}")

    # Save the cleaned workbook
    wb.save(output_path)
    print(f"✅ Cleaned file saved as: {output_path}")

def format_excel_file(file_path):
    """Ensure proper formatting of the Excel file."""
    # Load the workbook
    wb = openpyxl.load_workbook(file_path)
    ws = wb.active

    # Define styles
    header_fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

    # Apply styles to all cells
    for row in ws.iter_rows():
        for cell in row:
            if not isinstance(cell, openpyxl.cell.cell.MergedCell):  # Skip merged cells
                cell.border = border

                # Center alignment for date columns
                if cell.column >= 6:
                    cell.alignment = center_alignment

    # Apply header styles
    for cell in ws[1]:
        if not isinstance(cell, openpyxl.cell.cell.MergedCell):  # Skip merged cells
            cell.fill = header_fill
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

    # Find the total row
    total_row = None
    for row_idx, row in enumerate(ws.iter_rows(), 1):
        if row[0].value == 'Total':
            total_row = row_idx
            break

    if total_row:
        # Apply total row styles
        for cell in ws[total_row]:
            if not isinstance(cell, openpyxl.cell.cell.MergedCell):  # Skip merged cells
                cell.font = Font(bold=True)
                cell.fill = header_fill

    # Auto-adjust column widths (safely handling merged cells)
    for col_idx in range(1, ws.max_column + 1):
        max_length = 0
        column_letter = get_column_letter(col_idx)

        for row_idx in range(1, ws.max_row + 1):
            cell = ws.cell(row=row_idx, column=col_idx)
            if not isinstance(cell, openpyxl.cell.cell.MergedCell) and cell.value:
                cell_length = len(str(cell.value))
                if cell_length > max_length:
                    max_length = cell_length

        adjusted_width = max_length + 2
        ws.column_dimensions[column_letter].width = adjusted_width

    # Save the workbook
    wb.save(file_path)
    print(f"✅ Formatting applied to: {file_path}")

try:
    # Remove rows with zeros and save the cleaned file
    remove_rows_with_zeros(input_file, output_file)

    # Apply formatting to ensure it looks good
    format_excel_file(output_file)

except Exception as e:
    print(f"❌ Error processing file: {e}")
    import traceback
    traceback.print_exc()
