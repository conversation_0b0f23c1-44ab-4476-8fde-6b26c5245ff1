import smtplib
from email.message import EmailMessage
import os
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

load_dotenv()

class EmailDispatcher:
    """
    Agent responsible for sending emails with attachments.

    For Gmail:
    1. Use an App Password (RECOMMENDED):
       - Enable 2-Step Verification on your Google Account
       - Go to your Google Account > Security > App passwords
       - Select "Mail" and "Other (Custom name)"
       - Enter a name like "Marriage Quotes App"
       - Copy the 16-character password (without spaces) to your .env file

    2. App Password Benefits:
       - Works with personal Gmail accounts
       - More secure than your regular password
       - Bypasses Gmail's restrictions on "less secure apps"
       - Can be revoked at any time without changing your main password

    3. If you're still having issues, check:
       - https://accounts.google.com/DisplayUnlockCaptcha
    """

    def __init__(self):
        """Initialize the EmailDispatcher."""
        self.email_address = os.getenv('EMAIL_ADDRESS')
        self.email_password = os.getenv('EMAIL_PASSWORD')
        self.smtp_server = os.getenv('SMTP_SERVER')
        self.smtp_port = int(os.getenv('SMTP_PORT'))

        # Validate configuration
        if not all([self.email_address, self.email_password, self.smtp_server, self.smtp_port]):
            logger.warning("Email configuration incomplete. Check your .env file.")

    def send_email(self, subject, body, to, attachment_path=None):
        """
        Send an email with an optional attachment.

        Args:
            subject (str): Email subject
            body (str): Email body content
            to (str): Recipient email address
            attachment_path (str, optional): Path to file to attach

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            from email.mime.multipart import MIMEMultipart
            from email.mime.text import MIMEText
            from email.mime.image import MIMEImage

            # Check if body contains markdown formatting
            contains_markdown = any(marker in body for marker in ['#', '##', '*', '```', '---'])

            if contains_markdown:
                # If body contains markdown, use HTML for better formatting
                try:
                    import markdown
                    html_body = markdown.markdown(body)

                    # Create message with HTML
                    msg = MIMEMultipart('alternative')
                    msg['Subject'] = subject
                    msg['From'] = self.email_address
                    msg['To'] = to

                    # Attach plain text and HTML versions
                    msg.attach(MIMEText(body, 'plain'))
                    msg.attach(MIMEText(html_body, 'html'))

                except ImportError:
                    # If markdown module is not available, fall back to plain text
                    logger.warning("Markdown module not available, sending as plain text")
                    msg = EmailMessage()
                    msg['Subject'] = subject
                    msg['From'] = self.email_address
                    msg['To'] = to
                    msg.set_content(body)
            else:
                # Use simple EmailMessage for plain text
                msg = EmailMessage()
                msg['Subject'] = subject
                msg['From'] = self.email_address
                msg['To'] = to
                msg.set_content(body)

            # Add attachment if provided
            if attachment_path and os.path.exists(attachment_path):
                with open(attachment_path, 'rb') as f:
                    file_data = f.read()
                    file_name = os.path.basename(attachment_path)

                    if isinstance(msg, EmailMessage):
                        msg.add_attachment(file_data, maintype='image', subtype='png', filename=file_name)
                    else:
                        # For MIMEMultipart
                        image = MIMEImage(file_data)
                        image.add_header('Content-Disposition', 'attachment', filename=file_name)
                        msg.attach(image)
            elif attachment_path:
                logger.warning(f"Attachment file not found: {attachment_path}")

            # Connect to SMTP server and send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as smtp:
                smtp.starttls()
                smtp.login(self.email_address, self.email_password)
                smtp.send_message(msg)

            logger.info(f"Email sent successfully to {to}")
            return True

        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            return False

    def send_weekly_email(self, subject, body, to, attachment_paths=None):
        """
        Send an email with multiple attachments for weekly quotes.

        Args:
            subject (str): Email subject
            body (str): Email body content
            to (str): Recipient email address
            attachment_paths (list, optional): List of paths to files to attach

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            from email.mime.multipart import MIMEMultipart
            from email.mime.text import MIMEText
            from email.mime.image import MIMEImage
            from email.mime.application import MIMEApplication
            import mimetypes

            # Check if body contains markdown formatting
            contains_markdown = any(marker in body for marker in ['#', '##', '*', '```', '---'])

            # Create message
            if contains_markdown:
                # If body contains markdown, use alternative for HTML
                msg = MIMEMultipart('alternative')

                try:
                    import markdown
                    html_body = markdown.markdown(body)

                    # Add plain text and HTML versions
                    msg.attach(MIMEText(body, 'plain'))
                    msg.attach(MIMEText(html_body, 'html'))

                except ImportError:
                    # If markdown module is not available, fall back to plain text
                    logger.warning("Markdown module not available, sending as plain text")
                    msg = MIMEMultipart()
                    msg.attach(MIMEText(body))
            else:
                # Use simple multipart for plain text
                msg = MIMEMultipart()
                msg.attach(MIMEText(body))

            # Add headers
            msg['Subject'] = subject
            msg['From'] = self.email_address
            msg['To'] = to

            # Add attachments if provided
            if attachment_paths:
                for path in attachment_paths:
                    if os.path.exists(path):
                        with open(path, 'rb') as f:
                            file_data = f.read()
                            file_name = os.path.basename(path)

                            # Determine the MIME type
                            mime_type, _ = mimetypes.guess_type(path)

                            if mime_type and mime_type.startswith('image/'):
                                # Handle image attachments
                                _, subtype = mime_type.split('/', 1)
                                attachment = MIMEImage(file_data, _subtype=subtype)
                            else:
                                # Handle other file types
                                attachment = MIMEApplication(file_data)

                            attachment.add_header('Content-Disposition', 'attachment', filename=file_name)
                            msg.attach(attachment)
                    else:
                        logger.warning(f"Attachment file not found: {path}")

            # Connect to SMTP server and send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as smtp:
                smtp.starttls()
                smtp.login(self.email_address, self.email_password)
                smtp.send_message(msg)

            logger.info(f"Weekly email with {len(attachment_paths) if attachment_paths else 0} attachments sent successfully to {to}")
            return True

        except Exception as e:
            logger.error(f"Failed to send weekly email: {str(e)}")
            return False
