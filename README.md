# Marriage Quotes AI

An agentic AI system that generates marriage quotes, creates beautiful images with those quotes, and posts them to Instagram automatically.

## Features

- **Quote Generation**:
  - **GPT-Neo Model**: Uses the EleutherAI/gpt-neo-125M model for daily quotes
  - **OpenAI**: Uses OpenAI's API for weekly quotes
- **Image Creation**: Creates visually appealing images with the quotes
- **Instagram Posting**: Automatically posts the images to Instagram
- **Email Dispatch**: Sends the quotes via email
- **Daily Scheduler**: Sends a daily quote (default: every day at 9 AM)
- **Weekly Scheduler**: Sends quotes for the entire week every Monday at 7 AM

## Project Structure

```
marriage_quotes_ai/
├── agents/
│   ├── email_dispatcher.py       # Handles email sending
│   ├── image_creator.py          # Creates images with quotes
│   ├── instagram_poster.py       # Posts images to Instagram
│   ├── openai_quote_generator.py # Generates quotes using OpenAI
│   ├── quote_generator.py        # Generates quotes using Christian Q&A model
│   ├── scheduler.py              # Schedules daily quotes
│   └── weekly_scheduler.py       # Schedules weekly quotes
├── assets/                       # Directory for assets (created automatically)
├── output/                       # Directory for generated images
│   └── weekly/                   # Directory for weekly quote images
├── .env                          # Environment variables (create from .env.example)
├── .env.example                  # Example environment variables
├── .gitignore                    # Git ignore file
├── main.py                       # Main entry point for daily quotes
├── weekly_quotes.py              # Main entry point for weekly quotes
├── requirements.txt              # Python dependencies
├── setup.py                      # Setup script for dependencies
├── test_christian_quotes.py      # Test script for Christian Q&A model
├── test_email.py                 # Test script for email functionality
├── test_instagram.py             # Test script for Instagram posting
├── test_weekly_quotes.py         # Test script for weekly quotes
├── combined_scheduler.py         # Script to run both schedulers
└── README.md                     # This file
```

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Create a `.env` file from `.env.example` and fill in your credentials:
   ```
   cp .env.example .env
   ```
4. Edit the `.env` file with your email and Instagram credentials

### Gmail Configuration

To use Gmail for sending emails:

1. Set up your `.env` file with Gmail credentials:
   ```
   EMAIL_ADDRESS=<EMAIL>
   EMAIL_PASSWORD=your_app_password
   SMTP_SERVER=smtp.gmail.com
   SMTP_PORT=587
   RECIPIENT_EMAIL=<EMAIL>
   ```

2. For the `EMAIL_PASSWORD`, you should use an App Password:
   - Go to your Google Account > Security > 2-Step Verification
   - At the bottom, click on "App passwords"
   - Select "Mail" and "Other (Custom name)"
   - Enter a name like "Marriage Quotes App"
   - Copy the generated 16-character password to your `.env` file

3. If you're still having issues, check:
   - https://accounts.google.com/DisplayUnlockCaptcha

## Usage

### Running the Application

You can run the application in two ways:

1. **Scheduler Mode** - Runs on a schedule (default: daily at 9 AM):
   ```
   python main.py
   ```

2. **Immediate Mode** - Runs once immediately and then exits:
   ```
   python main.py --run-now
   ```

### Command Line Options

```
python main.py --help
```

Available options:
- `--run-now`: Run the job immediately and exit
- `--schedule`: Start the scheduler (default behavior)

### Testing Instagram Posting

To test only the Instagram posting functionality:

```
python test_instagram.py
```

## Dependencies

- transformers: For AI quote generation using the Christian Q&A model
- torch: Required for transformers library
- openai: For AI quote generation using OpenAI API
- Pillow: For image creation
- python-dotenv: For environment variable management
- APScheduler: For scheduling tasks
- instagrapi: For Instagram API integration

## GPT-Neo Model

The daily quotes are generated using the EleutherAI/gpt-neo-125M model from Hugging Face. This is a smaller version of GPT-Neo, which is an open-source alternative to GPT models, capable of generating creative and meaningful marriage quotes.

To test the GPT-Neo model:

```bash
python test_christian_quotes.py
```

## OpenAI Configuration

To use the OpenAI-based quote generator for weekly quotes:

1. Sign up for an OpenAI API key at https://platform.openai.com/
2. Add your API key to the `.env` file:
   ```
   OPENAI_API_KEY=your_openai_api_key
   ```

The system will automatically use the GPT-Neo model for daily quotes and OpenAI for weekly quotes. If either model fails, it will fall back to a predefined list of quotes.

## Customization

- Edit `agents/quote_generator.py` to change the GPT-Neo model or prompts
- Edit `agents/openai_quote_generator.py` to change the OpenAI prompts
- Edit `agents/image_creator.py` to customize the image style
- Edit `agents/scheduler.py` to change the daily schedule
- Edit `agents/weekly_scheduler.py` to change the weekly schedule

## Testing

### Test GPT-Neo Model

To test the GPT-Neo model for daily quotes:

```bash
python test_christian_quotes.py
```

### Test Email Functionality

To test the email functionality:

```bash
python test_email.py
```

### Test Instagram Posting

To test the Instagram posting functionality:

```bash
python test_instagram.py
```

### Test Weekly Quotes

To test the weekly quotes functionality:

```bash
python test_weekly_quotes.py
```

### Test Yearly Quotes

To generate 52 quotes (one for each week of the year) and send them via email:

```bash
python test_yearly_quotes.py
```

This script generates 26 quotes using the Christian Q&A model and 26 quotes using OpenAI, then sends them all in a single email. This is useful for previewing a year's worth of quotes at once.

## License

MIT
