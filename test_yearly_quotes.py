"""
Unit test for the quote_generator module.
Generates 52 quotes (one for each week of the year) using the Christian Q&A model
and sends them via email.
"""

import os
import logging
import time
import unittest
from datetime import datetime, timedelta
from dotenv import load_dotenv
from agents.quote_generator import QuoteGenerator
from agents.email_dispatcher import EmailDispatcher

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('yearly_quotes.log')
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class TestQuoteGenerator(unittest.TestCase):
    """Test case for the QuoteGenerator class."""

    def setUp(self):
        """Set up the test case."""
        self.quote_generator = QuoteGenerator()
        self.email_dispatcher = EmailDispatcher()

    def test_generate_quote(self):
        """Test that the generate_quote method returns a non-empty string."""
        quote = self.quote_generator.generate_quote()
        self.assertIsInstance(quote, str)
        self.assertTrue(len(quote) > 0)
        logger.info(f"Generated quote: {quote}")

    def test_generate_yearly_quotes(self):
        """Test generating 52 quotes for the year."""
        quotes = self.generate_yearly_quotes()
        self.assertEqual(len(quotes), 52)

        # Test sending the quotes via email
        email_body = self.format_email_body(quotes)
        recipient_email = os.getenv('RECIPIENT_EMAIL', '<EMAIL>')

        success = self.email_dispatcher.send_email(
            subject="Your 52 Marriage Quotes for the Year",
            body=email_body,
            to=recipient_email
        )

        self.assertTrue(success)
        logger.info(f"Yearly quotes email sent to {recipient_email}")

    def generate_yearly_quotes(self):
        """
        Generate 52 quotes (one for each week of the year) using the Christian Q&A model.

        Returns:
            list: List of tuples containing (week_number, quote, source)
        """
        # List to store quotes
        quotes = []

        # Generate 52 quotes
        logger.info("Generating 52 quotes for the year using Christian Q&A model...")

        for i in range(52):
            try:
                quote = self.quote_generator.generate_quote()
                quotes.append((i+1, quote, "Christian Q&A"))
                logger.info(f"Generated quote {i+1}/52")
                # Add a small delay to avoid overwhelming the model
                time.sleep(1)
            except Exception as e:
                logger.error(f"Error generating quote {i+1}: {str(e)}")
                # Use a fallback quote
                quotes.append((i+1, f"Quote {i+1} (fallback): Love is patient, love is kind.", "Fallback"))

        return quotes

    def format_email_body(self, quotes):
        """
        Format the quotes into an email body.

        Args:
            quotes (list): List of tuples containing (week_number, quote, source)

        Returns:
            str: Formatted email body
        """
        # Get the current year
        current_year = datetime.now().year

        # Start with a header
        body = f"# Marriage Quotes for {current_year}\n\n"
        body += "Here are 52 marriage quotes for each week of the year:\n\n"

        # Add each quote
        for week_num, quote, source in quotes:
            # Calculate the date for this week
            date = datetime.now().replace(month=1, day=1) + timedelta(weeks=week_num-1)
            date_str = date.strftime("%B %d")

            body += f"## Week {week_num} (around {date_str})\n\n"
            body += f"\"{quote}\"\n\n"
            body += f"*Source: {source}*\n\n"
            body += "---\n\n"

        # Add a footer
        body += "\nThese quotes were generated using the Marriage Quotes AI system.\n"
        body += "The system uses the Christian Q&A model to generate biblically-based marriage quotes.\n\n"
        body += "Enjoy your year of marriage quotes!"

        return body


def run_test():
    """Run the test to generate 52 quotes and send them via email."""
    print("Running unit test to generate 52 marriage quotes and send them via email...")
    print("This may take a few minutes. Please wait...")

    # Create test instance
    test = TestQuoteGenerator()
    test.setUp()

    try:
        # Run the test
        test.test_generate_yearly_quotes()
        print("\nYearly quotes email sent successfully!")
    except Exception as e:
        print(f"\nFailed to send yearly quotes email: {str(e)}")
        logger.error(f"Test failed: {str(e)}")

    print("\nDone!")


if __name__ == "__main__":
    run_test()
