# Environment variables
.env
.env.*
!.env.example
test_quote_*.png

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Output files
output/
*.png
instagram_session.json

# Logs
*.log
logs/
marriage_quotes.log

# Virtual Environment
venv/
env/
ENV/
.venv/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Jupyter Notebook
.ipynb_checkpoints

# PyTorch/TensorFlow model files
*.pt
*.pth
*.h5
*.pb
*.onnx
