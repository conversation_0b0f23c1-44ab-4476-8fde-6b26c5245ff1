"""
Test script for the GPT-Neo quote generator.
This script generates multiple quotes using the EleutherAI/gpt-neo-125M model.
"""

from agents.quote_generator import QuoteGenerator
import time

def test_gpt_neo_quotes():
    """Test the GPT-Neo quote generator."""
    print("Starting GPT-Neo quote generator test...")

    # Initialize the quote generator
    quote_generator = QuoteGenerator()

    # Generate and print 5 quotes
    print("\nGenerating 5 quotes from the GPT-Neo model:")
    print("-" * 60)

    for i in range(5):
        # Generate a quote
        quote = quote_generator.generate_quote()

        # Print the quote
        print(f"Quote {i+1}: \"{quote}\"")
        print("-" * 60)

        # Add a small delay to avoid overwhelming the model
        time.sleep(1)

    print("\nGPT-Neo quote generator test completed.")

if __name__ == "__main__":
    test_gpt_neo_quotes()
